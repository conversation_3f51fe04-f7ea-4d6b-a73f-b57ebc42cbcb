/* 
<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>
0402425
COMP B
*/

--SELECT

SELECT 'Hello, world!';

SELECT 'Hello, world!' AS Greeting;

SELECT 42;

SELECT 9 * 8;

SELECT 9 * 8 = 72;

SELECT true;

SELECT false;

SELECT 100 AS 'My Score';

SELECT 100 AS 'My Score',
    95 AS 'Your Score';

SELECT CURDATE() AS 'Today';

SELECT NOW() AS 'Current Date and Time';

-----------------------------------------------------------------------
-----------------------------------------------------------------------

--FROM

SELECT name FROM continents;

SELECT region_id, name FROM regions;

SELECT country_id, name, area, national_day
FROM countries;

SELECT country_id AS 'Database Id',
    country_code2 AS '2-digit Code',
    country_code3 AS '3-digit Code',
    name AS 'Country'
FROM countries;

SELECT * FROM country_stats;

---------------------------------------------------------------------
---------------------------------------------------------------------

--WHERE

SELECT name, area
FROM countries
WHERE area > 1000;

SELECT name, area
FROM countries
WHERE area <=1000;

SELECT country_id, name 
FROM countries
WHERE country_id =117;

SELECT name, national_day
FROM countries
WHERE name = 'Canada';

SELECT name
FROM countries
WHERE name LIKE 'Ca%';

SELECT country_id, name
FROM countries
WHERE country_id % 5 = 0;

SELECT country_id, name, area
FROM countries
WHERE area > 100000
    OR country_id > 100;

SELECT country_id, `year`, gdp, population
FROM country_stats
WHERE `year` BETWEEN 1990 AND 1999
    AND country_id = 14;

----------------------------------------------------------------------
----------------------------------------------------------------------

--ORDER BY

SELECT name, area, national_day
FROM countries
WHERE national_day IS NOT NULL
ORDER BY area ASC;

SELECT name, area, national_day
FROM countries
WHERE national_day IS NOT NULL
ORDER BY national_day DESC;

SELECT name, area, national_day
FROM countries
WHERE national_day IS NOT NULL
ORDER BY YEAR(national_day) DESC, area ASC;

----------------------------------------------------------------------
----------------------------------------------------------------------

--Challeges

SELECT name 
FROM countries
WHERE name LIKE 'ch%';

SELECT name, area
FROM countries
ORDER BY area DESC;

SELECT * FROM regions
ORDER BY name ASC;

SELECT name
FROM countries
WHERE country_id = 35;

SELECT name
FROM countries
WHERE national_day IS NULL;