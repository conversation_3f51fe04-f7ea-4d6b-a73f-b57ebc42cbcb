#!/usr/bin/env python3
"""
Script to generate INSERT statements from CSV file for MySQL
This will create a complete SQL file with all INSERT statements
"""

def generate_sql_inserts():
    # Read the CSV file
    with open('modualD(Sheet1).csv', 'r', encoding='utf-8') as file:
        names = [line.strip() for line in file if line.strip()]
    
    # Create the complete SQL script
    sql_content = '''/*
================================================================================
COMP-1701 Database Fundamentals
Competency D Assignment
================================================================================
Student Name: <PERSON><PERSON><PERSON>ani
Student ID: 0402425
Date: 2025-01-02
Assignment: Competency D - Database Creation and Data Loading

Description: This script creates a MySQL database named 'comp_d', creates a 
'people' table, and loads data using INSERT statements. It includes verification 
queries to confirm successful data loading.
================================================================================
*/

-- ============================================================================
-- STEP 1: DATABASE SETUP
-- ============================================================================

-- Drop the database if it already exists to ensure a clean start
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- ============================================================================
-- STEP 2: TABLE CREATION
-- ============================================================================

-- Create the people table with the required structure
CREATE TABLE people (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL
);

-- ============================================================================
-- STEP 3: DATA LOADING USING INSERT STATEMENTS
-- ============================================================================

-- Insert all names from CSV file
'''
    
    # Add INSERT statements in batches of 1000 for better performance
    batch_size = 1000
    for i in range(0, len(names), batch_size):
        batch = names[i:i + batch_size]
        sql_content += f"\n-- Batch {i//batch_size + 1}: Records {i+1} to {min(i+batch_size, len(names))}\n"
        sql_content += "INSERT INTO people (full_name) VALUES\n"
        
        for j, name in enumerate(batch):
            # Escape single quotes in names
            escaped_name = name.replace("'", "''")
            if j == len(batch) - 1:  # Last item in batch
                sql_content += f"('{escaped_name}');\n"
            else:
                sql_content += f"('{escaped_name}'),\n"
    
    # Add verification queries
    sql_content += '''
-- ============================================================================
-- STEP 4: DATA VERIFICATION
-- ============================================================================

-- Display the total number of rows loaded
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Preview the first 10 records to verify data was loaded correctly
SELECT * FROM people LIMIT 10;

-- ============================================================================
-- END OF SCRIPT
-- ============================================================================
'''
    
    # Write to new SQL file
    with open('comp_d_with_inserts.sql', 'w', encoding='utf-8') as file:
        file.write(sql_content)
    
    print(f"Generated SQL file with {len(names)} INSERT statements")
    print("File saved as: comp_d_with_inserts.sql")

if __name__ == "__main__":
    generate_sql_inserts()
