/* 
<PERSON><PERSON><PERSON>
0402425
COMP D
 
This script creates a MySQL database named 'comp_d', creates a
'people' table, and loads data from a CSV file. It includes verification
queries to confirm successful data loading.
*/

-- Drop the database if it already exists
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- Create the people table
CREATE TABLE people (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL
);

-- Enable local file loading
SET GLOBAL local_infile = 1;

-- Insert first batch of names (1-50)
INSERT INTO people (full_name) VALUES
('<PERSON><PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON><PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON><PERSON>'),
('<PERSON><PERSON><PERSON>'),
('<PERSON>'),
('<PERSON><PERSON><PERSON>'),
('<PERSON>'),
('<PERSON><PERSON>'),
('<PERSON><PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON><PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON>'),
('<PERSON><PERSON>'),
('<PERSON> <PERSON>'),
('<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>'),
('<PERSON><PERSON> <PERSON><PERSON>'),
('<PERSON><PERSON><PERSON> <PERSON>'),
('<PERSON><PERSON> <PERSON>'),
('<PERSON> <PERSON>'),
('<PERSON><PERSON> <PERSON>'),
('<PERSON><PERSON> <PERSON>'),
('<PERSON>'),
('<PERSON><PERSON><PERSON> <PERSON>'),
('<PERSON><PERSON> <PERSON>'),
('<PERSON>'),
('<PERSON> <PERSON><PERSON><PERSON>'),
('<PERSON><PERSON> Nguyen'),
('Yusuf Solis'),
('Edward Sanders'),
('Siena Weeks'),
('Ingrid Lowe'),
('Esther Lamb'),
('Izabella Ward'),
('Dahlia Meyers'),
('Mathias Esparza'),
('Edgar Howell'),
('Paityn Roth'),
('Estrella Prince'),
('Brylee Lucero'),
('Russell Johnston'),
('Dexter Hunt');

-- Display the total number of rows loaded
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Preview the first 10 records
SELECT * FROM people LIMIT 10;
