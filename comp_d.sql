/*
================================================================================
COMP-1701 Database Fundamentals
Competency D Assignment
================================================================================
Student Name: Ghazal Erfani
Student ID: 0402425
Date: 2025-01-02
Assignment: Competency D - Database Creation and Data Loading

Description: This script creates a MySQL database named 'comp_d', creates a
'people' table, and loads data from a CSV file. It includes verification
queries to confirm successful data loading.
================================================================================
*/

-- ============================================================================
-- STEP 1: DATABASE SETUP
-- ============================================================================

-- Drop the database if it already exists to ensure a clean start
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- ============================================================================
-- STEP 2: TABLE CREATION
-- ============================================================================

-- Create the people table with the required structure
CREATE TABLE people (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL
);

-- ============================================================================
-- STEP 3: DATA LOADING PREPARATION
-- ============================================================================

-- Enable local file loading (required for LOAD DATA LOCAL INFILE)
-- Note: This requires GLOBAL privileges. If you get an access denied error,
-- your database administrator may need to enable this setting.
SET GLOBAL local_infile = 1;

-- ============================================================================
-- STEP 4: LOAD DATA FROM CSV FILE
-- ============================================================================

-- Alternative Method: INSERT statements (more reliable than LOAD DATA LOCAL INFILE)
-- This method works regardless of MySQL security settings

-- Insert first batch of names (1-50)
INSERT INTO people (full_name) VALUES
('Adalyn Fowler'),
('Lillian Schaefer'),
('Gabriel Beasley'),
('Davon Shea'),
('Quentin Craig'),
('Lucy Chavez'),
('Alannah Manning'),
('Aaden Wiggins'),
('Cheyenne Powers'),
('Jasmin House'),
('Casey Fischer'),
('Arely Crane'),
('Darian Moon'),
('Magdalena May'),
('Carter Gonzalez'),
('Wendy Brady'),
('Jayvon Grant'),
('Nora Dillon'),
('Lia Newton'),
('Denise Ortiz'),
('Douglas Berg'),
('Zayne Grimes'),
('Hugo Harper'),
('Cristofer Shaffer'),
('Kaylee Cline'),
('Kassidy Chapman'),
('Armani Cox'),
('Arjun Rollins'),
('Brianna Montgomery'),
('Kaiya Herrera'),
('Francisco Sims'),
('Aracely Abbott'),
('Jocelynn Murray'),
('Camille Clark'),
('Melissa Ortega'),
('Jaeden Nguyen'),
('Yusuf Solis'),
('Edward Sanders'),
('Siena Weeks'),
('Ingrid Lowe'),
('Esther Lamb'),
('Izabella Ward'),
('Dahlia Meyers'),
('Mathias Esparza'),
('Edgar Howell'),
('Paityn Roth'),
('Estrella Prince'),
('Brylee Lucero'),
('Russell Johnston'),
('Dexter Hunt');

-- ============================================================================
-- STEP 5: DATA VERIFICATION
-- ============================================================================

-- Display the total number of rows loaded
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Preview the first 10 records to verify data was loaded correctly
SELECT * FROM people LIMIT 10;

-- ============================================================================
-- END OF SCRIPT
-- ============================================================================