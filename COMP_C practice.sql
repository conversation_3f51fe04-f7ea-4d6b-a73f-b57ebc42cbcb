/* 
Ghazal Erfani
0402425
COMP B
*/

--Module C.2 - INSERT

DESCRIBE countries;

INSERT INTO countries 
    (name, area, national_day, country_code2, country_code3, region_id)
VALUES ('Republic of Possum', 12345, '2025-08-06', 'RP', 'RPO', 1);

SELECT * 
FROM countries
WHERE name = 'Republic of Possum';

INSERT INTO countries 
    (name, area, national_day, country_code2, country_code3, region_id)
VALUES ('Ghazal', 65115, '2020-07-09', 'GO', 'GHZ', 5);

SELECT * 
FROM countries
WHERE name = 'Ghazal';

INSERT INTO countries 
    (name, area, national_day, country_code2, country_code3, region_id)
VALUES ('Red River College', 44784, '1990-07-01', 'RR', 'RRC', 7);

SELECT * 
FROM countries
WHERE name = 'Red River College';

--------------------------------------------------------------------------
--------------------------------------------------------------------------

--Module C.3 - UPDATE

UPDATE countries
SET national_day = '2025-08-06'
WHERE name = 'Canada';

UPDATE countries
SET area = 123456,
	national_day = NULL,
    region_id = 4
WHERE country_id = 23;

UPDATE countries
SET area = 200,
	national_day = CURDATE()
WHERE region_id = 4;

----------------------------------------------------------------------
----------------------------------------------------------------------

--Module C.3 - DELETE

DELETE FROM country_languages
WHERE country_id = 14;

DELETE FROM country_stats
WHERE `year` < 1990;

--------------------------------------------------
--------------------------------------------------

--Module C.4 - Intermediate SELECT Operations

SELECT name, area,
	(SELECT ROUND(AVG(area)) FROM countries) as avg_area
FROM countries;

SELECT name, area, national_day
FROM (
    SELECT name, area, national_day
    FROM countries
    WHERE national_day IS NOT NULL
) AS derived_table
WHERE name LIKE 'T%'
ORDER BY national_day;

SELECT name, region_id
FROM countries
WHERE region_id = (
    SELECT region_id FROM countries
    WHERE name = 'France'
);

SELECT name
FROM countries
WHERE region_id IN (
    SELECT region_id FROM regions
    WHERE continent_id IN (
        SELECT continent_id FROM continents
        WHERE name = 'North America'
    )
);