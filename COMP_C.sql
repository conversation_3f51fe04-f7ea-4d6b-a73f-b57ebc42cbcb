/* 
<PERSON><PERSON><PERSON>
0402425
COMP B
*/

--2
-- Retrieve records with these labels and assosiated values
SELECT 
    FALSE AS small,
    TRUE AS predator,
    TRUE AS claws,
    FALSE AS feathers;

--3
-- Join together strings to make a sentence
SELECT 
CONCAT(
    'There ', 'is ',
    'only ','one ','Lord ',
    'of ', 'the ', 'Rings'
) AS `Fact`;

--4.1
-- Show the national_day in a different format
SELECT name, 
DATE_FORMAT (
    national_day, '%M, %d, %Y'
) AS `national day`
FROM countries
WHERE region_id = 1;

--4.2
--Replace NULL national_day with 'No National Day'
SELECT name, region_id, 
IFNULL (
    national_day, 'No National Day'
) AS  `national day`
FROM countries
WHERE region_id = 1;

--4.3
-- Search for countries with 'United' in the name
SELECT name
FROM countries
WHERE INSTR(name, 'United');

--5
---- Retrieve a country as "My Favorite Country"
SELECT name AS "My Favorite Country"
FROM countries
WHERE name = 'Canada';