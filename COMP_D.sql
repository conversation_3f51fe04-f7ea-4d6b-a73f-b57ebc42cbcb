/* 
<PERSON><PERSON><PERSON>
0402425
COMP B
*/

-- Create the database if it does not exist
CREATE DATABASE IF NOT EXISTS comp_d;

-- Switch to using this database
USE comp_d;

-- Create the people table
CREATE TABLE IF NOT EXISTS people (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL DEFAULT ""
);

-- Load 10,000 names from CSV into the people table
LOAD DATA LOCAL INFILE "C:/Users/<USER>/Downloads/server/COMP D/modual D(Sheet1).csv"
INTO TABLE people
(full_name);