/*
================================================================================
COMP-1701 Database Fundamentals
Competency D Assignment
================================================================================
Student Name: Ghazal Erfani
Student ID: 0402425
Date: 2025-01-02
Assignment: Competency D - Database Creation and Data Loading

Description: This script creates a MySQL database named 'comp_d', creates a
'people' table, and loads data from a CSV file. It includes verification
queries to confirm successful data loading.
================================================================================
*/

-- ============================================================================
-- STEP 1: DATABASE SETUP
-- ============================================================================

-- Drop the database if it already exists to ensure a clean start
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- ============================================================================
-- STEP 2: TABLE CREATION
-- ============================================================================

-- Create the people table with the required structure
CREATE TABLE people (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL
);

-- ============================================================================
-- STEP 3: DATA LOADING PREPARATION
-- ============================================================================

-- Enable local file loading (required for LOAD DATA LOCAL INFILE)
SET SESSION local_infile = 1;

-- ============================================================================
-- STEP 4: LOAD DATA FROM CSV FILE
-- ============================================================================

-- Load data from CSV file into the people table
-- NOTE: Adjust the file path as needed for your system
-- If your CSV has a header row, uncomment the IGNORE 1 LINES option below

LOAD DATA LOCAL INFILE 'C:/Users/<USER>/OneDrive - Red River College Polytech/Documents/red river/data bases/server/modualD(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
-- IGNORE 1 LINES  -- Uncomment this line if your CSV has a header row
(full_name);

-- ============================================================================
-- STEP 5: DATA VERIFICATION
-- ============================================================================

-- Display the total number of rows loaded
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Preview the first 10 records to verify data was loaded correctly
SELECT * FROM people LIMIT 10;

-- ============================================================================
-- END OF SCRIPT
-- ============================================================================