-- Active: 1757514664355@@127.0.0.1@3306@comp_d
/*
<PERSON><PERSON><PERSON>
0402425
COMP D - Part 1

This script creates a MySQL database named 'comp_d', creates a
'people' table, and loads data from a CSV file using LOAD DATA LOCAL INFILE.
It includes verification queries to confirm successful data loading.
*/

-- Drop the database if it already exists
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- Create the people table
CREATE TABLE people (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL
);

-- ============================================================================
-- DATA IMPORT INSTRUCTIONS
-- ============================================================================
-- RECOMMENDED METHOD: Use MySQL Workbench Table Data Import Wizard
--
-- STEPS:
-- 1. Run this script up to this point (creates database and table)
-- 2. In MySQL Workbench Navigator, right-click on 'people' table
-- 3. Select "Table Data Import Wizard"
-- 4. Browse to: modual D(Sheet1).csv
-- 5. Map CSV column to 'full_name' field
-- 6. Import all records
-- 7. Run verification queries below
--
-- ALTERNATIVE: If you must use LOAD DATA LOCAL INFILE, try these options:

-- Option 1: Enable local_infile and try relative path
/*
SET GLOBAL local_infile = 1;
LOAD DATA LOCAL INFILE './modual D(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
(full_name);
*/

-- Option 2: Try with absolute path (forward slashes)
/*
SET GLOBAL local_infile = 1;
LOAD DATA LOCAL INFILE 'C:/Users/<USER>/OneDrive - Red River College Polytech/Documents/red river/data bases/server/modual D(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
(full_name);
*/

-- Option 3: Try with escaped backslashes
/*
SET GLOBAL local_infile = 1;
LOAD DATA LOCAL INFILE 'C:\\Users\\<USER>\\OneDrive - Red River College Polytech\\Documents\\red river\\data bases\\server\\modual D(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
(full_name);
*/

-- Verify data was loaded successfully
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Display the first 50 rows as required by assignment (Step 10)
SELECT full_name FROM people LIMIT 50;
