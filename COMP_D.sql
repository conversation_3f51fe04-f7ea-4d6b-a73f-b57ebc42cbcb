-- Active: 1757514664355@@127.0.0.1@3306@comp_d
/*
<PERSON><PERSON><PERSON>
0402425
COMP D - Part 1

This script creates a MySQL database named 'comp_d', creates a
'people' table, and loads data from a CSV file using LOAD DATA LOCAL INFILE.
It includes verification queries to confirm successful data loading.
*/

-- Drop the database if it already exists
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- Create the people table
CREATE TABLE people (
    id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL
);

-- Enable local file loading (required for LOAD DATA LOCAL INFILE)
SET GLOBAL local_infile = 1;

-- LOAD DATA LOCAL INFILE command to import CSV data
-- Try these options in order until one works:

-- Option 1: Relative path (if running from the same directory as CSV)
LOAD DATA LOCAL INFILE './modualD(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
(full_name);

-- Option 2: If Option 1 fails, comment it out and try this absolute path
/*
LOAD DATA LOCAL INFILE 'C:/Users/<USER>/OneDrive - Red River College Polytech/Documents/red river/data bases/server/modualD(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
(full_name);
*/

-- Option 3: Alternative path format with escaped backslashes
/*
LOAD DATA LOCAL INFILE 'C:\\Users\\<USER>\\OneDrive - Red River College Polytech\\Documents\\red river\\data bases\\server\\modualD(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY ','
OPTIONALLY ENCLOSED BY '"'
LINES TERMINATED BY '\n'
(full_name);
*/

-- Verify data was loaded successfully
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Display the first 50 rows as required by assignment (Step 10)
SELECT full_name FROM people LIMIT 50;
