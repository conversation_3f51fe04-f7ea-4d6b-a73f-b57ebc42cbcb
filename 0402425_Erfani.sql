/* 
<PERSON><PERSON><PERSON>
0402425
COMP B
*/

CREATE TABLE  "agents" 
   (	
	"agent_code" CHAR(6) NOT NULL PRIMARY KEY, 
	"agent_name" CHAR(40),

	"working_area" CHAR(35), 
	"commission" NUMBER(10,2), 

	"phone_no" CHAR(15), 
	"country" VARCHAR2(25) 
);

CREATE TABLE  "customer" 
   (	
	"cust_code" VARCHAR2(6) NOT NULL PRIMARY KEY,
	"cust_name" VARCHAR2(40) NOT NULL,
	"cust_city" CHAR(35), 
	"working_area" VARCHAR2(35) NOT NULL, 
	"cust_country" VARCHAR2(20) NOT NULL, 

	"grade" NUMBER, 
	"opening_amt" NUMBER(12,2) NOT NULL, 
	"receive_amt" NUMBER(12,2) NOT NULL, 
	"payment_amt" NUMBER(12,2) NOT NULL, 
	"outstanding_amt" NUMBER(12,2) NOT NULL, 

	"phone_no" VARCHAR2(17) NOT NULL, 
	"agent_code" CHAR(6) NOT NULL REFERENCES agents
);   



